#!/usr/bin/env node

// 测试本地版本的 CodeRocketService

import { CodeRocketService } from './dist/services/CodeRocketService.js';
import { ConfigManager } from './dist/config/ConfigManager.js';
import { PromptManager } from './dist/prompts/PromptManager.js';

async function testLocal() {
  console.log('=== 初始化系统组件 ===');
  
  // 初始化配置管理器
  await ConfigManager.initialize(true);
  console.log('ConfigManager 初始化完成');
  
  // 初始化提示词管理器
  await PromptManager.initialize();
  console.log('PromptManager 初始化完成');
  
  console.log('\n=== 创建 CodeRocketService 实例 ===');
  const service = new CodeRocketService();
  console.log('CodeRocketService 创建完成');
  
  console.log('\n=== 测试 AI 服务状态 ===');
  const status = await service.getAIServiceStatus();
  console.log('AI 服务状态:', JSON.stringify(status, null, 2));
}

testLocal().catch(console.error);
