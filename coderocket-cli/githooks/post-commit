#!/bin/bash

# 获取 Git 仓库根目录
REPO_ROOT=$(git rev-parse --show-toplevel 2>/dev/null)

# 如果不在 Git 仓库中，退出
if [ -z "$REPO_ROOT" ]; then
    echo "❌ 错误：不在 Git 仓库中"
    exit 1
fi

# 安全地加载必要的环境变量
# 只加载项目相关的环境变量，避免全局profile污染

# 加载项目环境文件
if [ -f "$REPO_ROOT/.env" ]; then
    # 只加载以特定前缀开头的环境变量，避免污染
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        [[ $key =~ ^[[:space:]]*# ]] && continue
        [[ -z $key ]] && continue

        # 只加载AI和GitLab相关的环境变量
        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|CLAUDECODE_) ]]; then
            export "$key=$value"
        fi
    done < "$REPO_ROOT/.env" 2>/dev/null
fi

# 加载全局CodeRocket配置
if [ -f "$HOME/.coderocket/env" ]; then
    while IFS='=' read -r key value; do
        [[ $key =~ ^[[:space:]]*# ]] && continue
        [[ -z $key ]] && continue

        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|CLAUDECODE_) ]]; then
            export "$key=$value"
        fi
    done < "$HOME/.coderocket/env" 2>/dev/null
elif [ -f "$HOME/.codereview-cli/env" ]; then   # backward-compat (remove in next major)
    while IFS='=' read -r key value; do
        [[ $key =~ ^[[:space:]]*# ]] && continue
        [[ -z $key ]] && continue
        if [[ $key =~ ^(AI_|GITLAB_|GEMINI_|CLAUDECODE_) ]]; then
            export "$key=$value"
        fi
    done < "$HOME/.codereview-cli/env" 2>/dev/null
fi

# 导入AI服务管理器
if [ -f "$REPO_ROOT/lib/ai-service-manager.sh" ]; then
    source "$REPO_ROOT/lib/ai-service-manager.sh"
elif [ -f "$HOME/.coderocket/lib/ai-service-manager.sh" ]; then
    source "$HOME/.coderocket/lib/ai-service-manager.sh"
elif [ -f "$HOME/.codereview-cli/lib/ai-service-manager.sh" ]; then   # backward-compat
    source "$HOME/.codereview-cli/lib/ai-service-manager.sh"
else
    echo "❌ 错误：AI服务管理器未找到"
    exit 1
fi

# 检查提示词文件是否存在
PROMPT_FILE="$REPO_ROOT/prompts/git-commit-review-prompt.md"
if [ ! -f "$PROMPT_FILE" ]; then
    echo "❌ 错误：提示词文件不存在: $PROMPT_FILE"
    exit 1
fi

# 获取当前AI服务
CURRENT_AI_SERVICE=$(get_ai_service)

# 检查AI服务是否可用
if ! check_ai_service_available "$CURRENT_AI_SERVICE"; then
    echo "❌ 错误：AI服务 $CURRENT_AI_SERVICE 不可用"
    echo "安装命令: $(get_install_command "$CURRENT_AI_SERVICE")"
    exit 1
fi

# 创建 review_logs 目录（如果不存在）
mkdir -p "$REPO_ROOT/review_logs"

echo "🚀 正在执行 commit 后的代码审查..."
echo "📡 使用AI服务: $CURRENT_AI_SERVICE"

# 切换到仓库根目录执行
cd "$REPO_ROOT"

# 准备更明确的提示词
PROMPT="请执行以下任务：
1. 你是代码审查专家，需要对最新的 git commit 进行审查
2. 使用 git --no-pager show 命令获取最新提交的详细信息
3. 根据提示词文件中的指导进行全面代码审查
4. 生成审查报告并保存到 review_logs 目录
5. 不要询问用户，直接自主执行所有步骤
6. 这是一个自动化流程，请直接开始执行"

if intelligent_ai_review "$CURRENT_AI_SERVICE" "$PROMPT_FILE" "$PROMPT"; then
    echo "👌 代码审查完成"
    echo "📝 审查报告已保存到 $REPO_ROOT/review_logs 目录"
else
    echo "❌ 代码审查失败，但不影响提交"
fi